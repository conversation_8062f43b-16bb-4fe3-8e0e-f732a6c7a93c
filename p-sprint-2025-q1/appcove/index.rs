#[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.add_body(html! {
            about-appcove {
                grid-2 {
                    // Left Column (AppCove Summary)
                    #page-heading {
                        div {
                            header {
                                h1 { "AppCove" }
                                p {
                                    "We build custom software focused on providing "
                                    strong { "your customers" }
                                    " with a premium experience."
                                }
                            }
                            #page-body {
                                header {
                                    p {
                                        "AppCove has a proven track record of building and supporting reliable web applications for over 20 years. "
                                        "We take your long term business goals seriously."
                                    }
                                }
                            }
                            #page-body {
                                header {
                                    content {
                                        "This is reflected in every aspect of our work — from our people and training to our software and contracts. "
                                        "While you focus on innovation and operations, we focus on delivering technical excellence, robust security, "
                                        "and flexible infrastructure — whether hosted with us or deployed on your cloud."
                                    }
                                }
                            }
                            #page-footer {
                                footer {
                                    p {
                                        strong { "FOUNDED" } "2003"
                                        br;
                                        strong { "OWNER" } "Jason Garber"
                                        br;
                                        strong { "SIZE" } "20+ Employees"
                                        br;
                                        strong { "HEADQUARTERS" } "Altoona, PA"
                                        br;
                                    }
                                }
                            }
                            #page-main {
                                grid-3 {
                                    header-2 { strong { "Problems" } }
                                    br;
                                    header-3 { strong { "Answers" } }
                                }
                                hr;
                                div {
                                    grid-3 {
                                        p { "Business Data is contained in a growing number of SaaS providers that do not communicate well with each other." }
                                        div class="arrow-icon-wrapper" {
                                            i class="fas fa-arrow-right arrow-icon" {}
                                        }
                                        p { "AppCove has an intense focus on well-designed custom databases for each client we work with. Having all key data in one central database allows everything else to fall into place." }
                                    }
                                    hr;
                                    grid-3 {
                                        p { "Premium Customer Experience can be difficult to provide when using a collection of off-the-shelf SaaS tools." }
                                        div class="arrow-icon-wrapper" {
                                            i class="fas fa-arrow-right arrow-icon" {}
                                        }
                                        p { "AppCove provides a unified experience with correct security and access controls covering all stakeholders: owners / administrators / staff / customers / end users." }
                                    }
                                    hr;
                                    grid-3 {
                                        p { "Custom software can be slow to build, with uncertain outcomes, and may lack long term support." }
                                        div class="arrow-icon-wrapper" {
                                            i class="fas fa-arrow-right arrow-icon" {}
                                        }
                                        p { "AppCove’s pursuit of technical excellence, coupled with a willingness to learn the details of your business results in efficient code, streamlined implementation, and a custom software product built for long-term support." }
                                    }
                                    hr;
                                    grid-3 {
                                        p { "Artificial intelligence fails to maximize value if it does not have access to reliable, structured business data" }
                                        div class="arrow-icon-wrapper" {
                                            i class="fas fa-arrow-right arrow-icon" {}
                                        }
                                        p { "AppCove’s rigorous database design approach positions each client ideally to participate in the rapid increase of AI capabilities." }
                                    }
                                }
                            }
                            #page-heading {
                                h4 { strong { "Key Staff" } }
                                grid-2 {
                                    div class="staff-card" {
                                        strong { "Jason Garber" }
                                        span { "President & Architect" }
                                        em { "22 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        strong { "Julie Garber" }
                                        span { "Director of Operations" }
                                        em { "22 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        strong { "Andrew Bidochko" }
                                        span { "Senior Engineer" }
                                        em { "21 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        strong { "Sergio Olivo" }
                                        span { "Technical Project Consultant" }
                                        em { "19 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        strong { "Jeff Berdin" }
                                        span { "Director of Infrastructure" }
                                        em { "18 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        strong { "Iryna Bidochko" }
                                        span { "Software Engineer" }
                                        em { "10 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        strong { "Jessi Garber" }
                                        span { "Senior UI/UX Developer" }
                                        em { "10 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        strong { "Don Berdin" }
                                        span { "Director of Support" }
                                        em { "10 YEARS WITH APPCOVE" }
                                    }
                                }
                            }
                            #page-footer {
                                footer {
                                    p {strong { "FOR BUSINESS INQUIRIES, CONTACT" }}
                                        grid-2 {
                                            p{
                                                "Jason Garber" br;
                                                "<EMAIL>" br;
                                                "(814) 240-3338"
                                            }
                                            p{
                                                "AppCove, Inc." br;
                                                "P.O. Box 1309" br;
                                                "Altoona PA 16603"
                                            }
                                        }
                                    }
                                }
                            }
                        }



                    #page-body {
                        div {
                            grid-2 {
                                div class="card timeline" {
                                    h2 { "Custom Software Deployment Timeline" }
                                    p class="subheading" { "A SELECTION OF NOTABLE PROJECTS" }
                                }
                            }
                            ul class="timeline-list" {
                                li {
                                    strong { "2004 — 21 Years " }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Advanced Reservation System" }
                                    p { "Connected staff, guides, clients, guests, housekeeping together in one unified scheduling portal for a Pennsylvania Fishing club." }
                                }
                                li {
                                    strong { "2005 — 15 Years " }
                                    strong { "[Inactive Project]" }
                                    br;
                                    span class="title" { "CRM & Marketing Software" }
                                    p { "Constructed a database system to collect and distribute millions of leads to thousands of small business owners nationwide via a unique filtering engine and CRM." }
                                }
                                li {
                                    strong { "2009 — 16 Years " }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Client Marketing Dashboard" }
                                    p { "Built a custom marketing planning, community, and content delivery platform used by thousands of small businesses to access premium content, community, and the tools they need to grow their businesses." }
                                }
                                li {
                                    strong { "2013 — 12 Years " }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Technical Investment in ACRM" }
                                    p { "Engineered and implemented a set of foundational modules that became the basis for all AppCove software going forward." }
                                }
                                li {
                                    strong { "2016 — 9 Years " }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Tools for Financial Advisors" }
                                    p { "Created custom software representing unique finance and investment products. Hundreds of financial advisors have used these to serve tens of thousands of clients." }
                                }
                                li {
                                    strong { "2018 — 7 Years " }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Technical Investment in ACE" }
                                    p { "Architected the AppCove Cloud Engine to consolidate cloud management, security, BDR, and deployment of custom software." }
                                }
                                li {
                                    strong { "2020 — 5 Years " }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Virtual Event Platform" }
                          #[approck::http(GET /; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(doc: Document) -> Response {
        use maud::html;

        doc.add_body(html! {
            about-appcove {
                grid-2 {
                    // Left Column (AppCove Summary)
                    #page-heading {
                        div {
                            header {
                                h1 { "AppCove" }
                                p {
                                    "We build custom software focused on providing "
                                    strong { "your customers" }
                                    " with a premium experience."
                                }
                            }
                            #page-body {
                                header {
                                    p {
                                        "AppCove has a proven track record of building and supporting reliable web applications for over 20 years. "
                                        "We take your long term business goals seriously."
                                    }
                                }
                            }
                            #page-body {
                                header {
                                    content {
                                        "This is reflected in every aspect of our work — from our people and training to our software and contracts. "
                                        "While you focus on innovation and operations, we focus on delivering technical excellence, robust security, "
                                        "and flexible infrastructure — whether hosted with us or deployed on your cloud."
                                    }
                                }
                            }
                            #page-footer {
                                footer {
                                    p {
                                        strong { "FOUNDED" } "2003"
                                        br;
                                        strong { "OWNER" } "Jason Garber"
                                        br;
                                        strong { "SIZE" } "20+ Employees"
                                        br;
                                        strong { "HEADQUARTERS" } "Altoona, PA"
                                        br;
                                    }
                                }
                            }
                            #page-main {
                                grid-3 {
                                    header-2 { strong { "Problems" } }
                                    br;
                                    header-3 { strong { "Answers" } }
                                }
                                hr;
                                div {
                                    grid-3 {
                                        p { "Business Data is contained in a growing number of SaaS providers that do not communicate well with each other." }
                                        div class="arrow-icon-wrapper" {
                                            i class="fas fa-arrow-right arrow-icon" {}
                                        }
                                        p { "AppCove has an intense focus on well-designed custom databases for each client we work with. Having all key data in one central database allows everything else to fall into place." }
                                    }
                                    hr;
                                    grid-3 {
                                        p { "Premium Customer Experience can be difficult to provide when using a collection of off-the-shelf SaaS tools." }
                                        div class="arrow-icon-wrapper" {
                                            i class="fas fa-arrow-right arrow-icon" {}
                                        }
                                        p { "AppCove provides a unified experience with correct security and access controls covering all stakeholders: owners / administrators / staff / customers / end users." }
                                    }
                                    hr;
                                    grid-3 {
                                        p { "Custom software can be slow to build, with uncertain outcomes, and may lack long term support." }
                                        div class="arrow-icon-wrapper" {
                                            i class="fas fa-arrow-right arrow-icon" {}
                                        }
                                        p { "AppCove’s pursuit of technical excellence, coupled with a willingness to learn the details of your business results in efficient code, streamlined implementation, and a custom software product built for long-term support." }
                                    }
                                    hr;
                                    grid-3 {
                                        p { "Artificial intelligence fails to maximize value if it does not have access to reliable, structured business data" }
                                        div class="arrow-icon-wrapper" {
                                            i class="fas fa-arrow-right arrow-icon" {}
                                        }
                                        p { "AppCove’s rigorous database design approach positions each client ideally to participate in the rapid increase of AI capabilities." }
                                    }
                                }
                            }
                            #page-heading {
                                h4 { strong { "Key Staff" } }
                                grid-2 {
                                    div class="staff-card" {
                                        strong { "Jason Garber" }
                                        span { "President & Architect" }
                                        em { "22 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        strong { "Julie Garber" }
                                        span { "Director of Operations" }
                                        em { "22 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        strong { "Andrew Bidochko" }
                                        span { "Senior Engineer" }
                                        em { "21 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        strong { "Sergio Olivo" }
                                        span { "Technical Project Consultant" }
                                        em { "19 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        strong { "Jeff Berdin" }
                                        span { "Director of Infrastructure" }
                                        em { "18 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        strong { "Iryna Bidochko" }
                                        span { "Software Engineer" }
                                        em { "10 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        strong { "Jessi Garber" }
                                        span { "Senior UI/UX Developer" }
                                        em { "10 YEARS WITH APPCOVE" }
                                    }
                                    div class="staff-card" {
                                        strong { "Don Berdin" }
                                        span { "Director of Support" }
                                        em { "10 YEARS WITH APPCOVE" }
                                    }
                                }
                            }
                            #page-footer {
                                footer {
                                    p {strong { "FOR BUSINESS INQUIRIES, CONTACT" }}
                                        grid-2 {
                                            p{
                                                "Jason Garber" br;
                                                "<EMAIL>" br;
                                                "(814) 240-3338"
                                            }
                                            p{
                                                "AppCove, Inc." br;
                                                "P.O. Box 1309" br;
                                                "Altoona PA 16603"
                                            }
                                        }
                                    }
                                }
                            }
                        }



                    #page-body {
                        div {
                            grid-2 {
                                div class="card timeline" {
                                    h2 { "Custom Software Deployment Timeline" }
                                    p class="subheading" { "A SELECTION OF NOTABLE PROJECTS" }
                                }
                            }
                            ul class="timeline-list" {
                                li {
                                    strong { "2004 — 21 Years " }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Advanced Reservation System" }
                                    p { "Connected staff, guides, clients, guests, housekeeping together in one unified scheduling portal for a Pennsylvania Fishing club." }
                                }
                                li {
                                    strong { "2005 — 15 Years " }
                                    strong { "[Inactive Project]" }
                                    br;
                                    span class="title" { "CRM & Marketing Software" }
                                    p { "Constructed a database system to collect and distribute millions of leads to thousands of small business owners nationwide via a unique filtering engine and CRM." }
                                }
                                li {
                                    strong { "2009 — 16 Years " }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Client Marketing Dashboard" }
                                    p { "Built a custom marketing planning, community, and content delivery platform used by thousands of small businesses to access premium content, community, and the tools they need to grow their businesses." }
                                }
                                li {
                                    strong { "2013 — 12 Years " }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Technical Investment in ACRM" }
                                    p { "Engineered and implemented a set of foundational modules that became the basis for all AppCove software going forward." }
                                }
                                li {
                                    strong { "2016 — 9 Years " }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Tools for Financial Advisors" }
                                    p { "Created custom software representing unique finance and investment products. Hundreds of financial advisors have used these to serve tens of thousands of clients." }
                                }
                                li {
                                    strong { "2018 — 7 Years " }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Technical Investment in ACE" }
                                    p { "Architected the AppCove Cloud Engine to consolidate cloud management, security, BDR, and deployment of custom software." }
                                }
                                li {
                                    strong { "2020 — 5 Years " }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Virtual Event Platform" }
                                    p { "Deployed a scalable virtual event platform with custom graphical themes, live session streaming, interactive chat, video chat, help desk, breakout rooms, sponsor booths, broadcast studio, pre-recorded content, gamification, and real-time notifications." }
                                }
                                li {
                                    strong { "2023" }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Technical Investment in ACE 2.0" }
                                    p { "ACE 2.0 is designed to cut through the complexity of the cloud, and deliver stable infrastructure to all custom applications we build going forward." }
                                }
                                li {
                                    strong { "2025" }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Technical Investment in approck" }
                                    p { "Engineered and implemented our next generation toolbox of software infrastructure, dev/ops, and database management tools." }
                                }
                            }
                        }
                    }
                }
            }
        });

        Response::HTML(doc.into())
    }
}
          p { "Deployed a scalable virtual event platform with custom graphical themes, live session streaming, interactive chat, video chat, help desk, breakout rooms, sponsor booths, broadcast studio, pre-recorded content, gamification, and real-time notifications." }
                                }
                                li {
                                    strong { "2023" }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Technical Investment in ACE 2.0" }
                                    p { "ACE 2.0 is designed to cut through the complexity of the cloud, and deliver stable infrastructure to all custom applications we build going forward." }
                                }
                                li {
                                    strong { "2025" }
                                    strong { "[Active Project]" }
                                    br;
                                    span class="title" { "Technical Investment in approck" }
                                    p { "Engineered and implemented our next generation toolbox of software infrastructure, dev/ops, and database management tools." }
                                }
                            }
                        }
                    }
                }
            }
        });

        Response::HTML(doc.into())
    }
}
